is_enabled: true
# round time in minutes
round_time: 20
# round end in seconds
round_end_time: 30
dummy_player_name: '[观察我重生]'
# Enable automatic server configuration modifications (FriendlyFire, etc.). Disable this if experiencing Remote Admin config issues.
enable_server_config_modifications: false
global_reference_config:
# [AUTO GENERATED FILE] may contain types which no longer work. A reference list of types to be used in other configs (do not edit)
  all_items: []
  all_effects: []
  all_attachments: []
  rooms_config:
    rooms_per_player: 2.5
    # how resistant surface is to changes in its open/close state. higher numbers will keep surface open/closed for longer
    surface_weight: 5
    # decon delay. broadcast msg is not sent until time left is less than DecontaminationCaution. we want to buffer this timer to prevent frequent changes in room decon state being seen by the player i.e. lights flickering from yellow to normal
    decontamination_time: 25
    # broadcast with low priority a caution message to the player. see BroadcastOverride.cs for details about priority
    decontamination_caution: 20
    # broadcast with medium priority a warning message to the player
    decontamination_warning: 14
    # broadcast with high priority a danger message to the player
    decontamination_danger: 7
    surface_decontamination_time_multiplier: 2
  killstreak_config:
  # if disabled the player cannot select RAGE killstreak even if they have found it
    rage_enabled: true
    # default killstreak must be included in the killstreak table otherwise the plugin will not load
    default_killstreak: Novice
    # you can define up to 7 killstreaks
# the RAGE killstreak in an easter egg that can only appear when found correctly
# Items rewards - Action [Add, Remove], Item [see global reference config for types]
# Effect rewards - Effect [see global reference config for types], Intensity [0-255], Duration[0=infinite, 1-255 seconds]
# Ammo rewards - Action [Add, Remove, Set], Stat [Inventory, Gun], Proportion [0.0-1.0]
# Player rewards - Action [Add, Remove, Set], Stat [HP, AHP, Stamina], Value [float], Sustain [float for AHP], Persistent [bool for AHP]
# Overflow action [End, Rollover, Clamp] Rollover will rollover to 1 not 0
# anything with a 0 killstreak items/ammo/effects/players stats are granted when the player spawns
    killstreak_tables:
      Noob:
        menu_item: KeycardJanitor
        menu_description: <color=#bdafe4>[清洁工]</color> = <b><color=#16a4d8>菜鸟</color> - 生成带有重型装甲和3个医疗包的新手，最大杀伤连击数限制为5</b>
        loadout_lock: false
        color_hex: '#16a4d8'
        item_overflow_action: End
        item_table:
          0:
          - action: Add
            item: ArmorHeavy
          - action: Add
            item: Medkit
          - action: Add
            item: Medkit
          - action: Add
            item: Medkit
          2:
          - action: Add
            item: Painkillers
          5:
          - action: Add
            item: GrenadeFlash
        ammo_overflow_action: Rollover
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 1
          1:
          - action: Add
            stat: Inventory
            proportion: 0.100000001
        player_overflow_action: End
        player_table: {}
        effect_overflow_action: End
        effect_table: {}
      Novice:
        menu_item: KeycardScientist
        menu_description: <color=#e7d77b>[科学家]</color> = <b><color=#60dbe8>新手</color></b> - 出生时拥有战斗装甲，医疗包和止痛药，最大连杀上限 10</b>
        loadout_lock: false
        color_hex: '#60dbe8'
        item_overflow_action: End
        item_table:
          0:
          - action: Add
            item: ArmorCombat
          - action: Add
            item: Medkit
          - action: Add
            item: Painkillers
          2:
          - action: Add
            item: Painkillers
          4:
          - action: Add
            item: Medkit
          6:
          - action: Add
            item: Painkillers
          8:
          - action: Add
            item: Medkit
          10:
          - action: Add
            item: Painkillers
          - action: Add
            item: GrenadeHE
        ammo_overflow_action: Rollover
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 1
          1:
          - action: Add
            stat: Inventory
            proportion: 0.150000006
        player_overflow_action: End
        player_table: {}
        effect_overflow_action: End
        effect_table: {}
      Intermediate:
        menu_item: KeycardMTFPrivate
        menu_description: <color=#accfe1>[列兵]</color> = <b><color=#8bd346>中级</color></b> - 出生时拥有战斗装甲和止痛药，最大连杀上限 15</b>
        loadout_lock: false
        color_hex: '#8bd346'
        item_overflow_action: End
        item_table:
          0:
          - action: Add
            item: ArmorCombat
          - action: Add
            item: Painkillers
          3:
          - action: Add
            item: Painkillers
          6:
          - action: Add
            item: Medkit
          8:
          - action: Add
            item: Painkillers
          9:
          - action: Add
            item: SCP330
          10:
          - action: Add
            item: Medkit
          - action: Add
            item: GrenadeFlash
          11:
          - action: Add
            item: SCP330
          12:
          - action: Add
            item: Painkillers
          13:
          - action: Add
            item: Medkit
          - action: Add
            item: SCP2176
          - action: Add
            item: SCP330
          14:
          - action: Add
            item: Adrenaline
          15:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP330
          - action: Add
            item: Jailbird
        ammo_overflow_action: Rollover
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 0.5
          1:
          - action: Add
            stat: Inventory
            proportion: 0.200000003
        player_overflow_action: End
        player_table: {}
        effect_overflow_action: End
        effect_table: {}
      Advanced:
        menu_item: KeycardMTFOperative
        menu_description: <color=#177dde>[特工钥匙卡]</color> = <b><color=#efdf48> 高级</color> - 轻甲生成，最大连杀数 20 </b>
        loadout_lock: false
        color_hex: '#efdf48'
        item_overflow_action: Clamp
        item_table:
          0:
          - action: Add
            item: ArmorLight
          1:
          - action: Add
            item: Painkillers
          2:
          - action: Add
            item: SCP330
          3:
          - action: Add
            item: Medkit
          4:
          - action: Add
            item: SCP330
          5:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP1853
          6:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          7:
          - action: Add
            item: Medkit
          8:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          9:
          - action: Add
            item: Painkillers
          10:
          - action: Remove
            item: ArmorLight
          - action: Add
            item: ArmorCombat
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          11:
          - action: Add
            item: Medkit
          12:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          - action: Add
            item: Adrenaline
          13:
          - action: Add
            item: Painkillers
          - action: Add
            item: Adrenaline
          14:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          - action: Add
            item: Adrenaline
          15:
          - action: Remove
            item: ArmorCombat
          - action: Add
            item: ArmorHeavy
          - action: Add
            item: SCP244a
          16:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          - action: Add
            item: Adrenaline
          17:
          - action: Add
            item: Painkillers
          - action: Add
            item: Jailbird
          18:
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          - action: Add
            item: Adrenaline
          19:
          - action: Add
            item: Medkit
          - action: Add
            item: SCP268
          20:
          - action: Add
            item: SCP500
          - action: Add
            item: SCP330
          - action: Add
            item: SCP330
          - action: Add
            item: Adrenaline
        ammo_overflow_action: Rollover
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 1
          1:
          - action: Add
            stat: Inventory
            proportion: 0.25
          - action: Add
            stat: Gun
            proportion: 0.25
        player_overflow_action: Clamp
        player_table:
          5:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          6:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          7:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          8:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          9:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          10:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 0
            persistent: false
          11:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 0
            persistent: false
          12:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 0
            persistent: false
          13:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 0
            persistent: false
          14:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 0
            persistent: false
          15:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          16:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          17:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          18:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          19:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          20:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 30
            sustain: 0
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
        effect_overflow_action: End
        effect_table:
          7:
          - effect: MovementBoost
            intensity: 10
            duration: 0
          15:
          - effect: MovementBoost
            intensity: 15
            duration: 0
          20:
          - effect: MovementBoost
            intensity: 20
            duration: 0
      Expert:
        menu_item: KeycardMTFCaptain
        menu_description: <color=#1b43cb>[指挥官]</color> = <b><color=#f9a52c> 专家</color> - 轻甲生成，最大连杀数 25+ </b>
        loadout_lock: true
        color_hex: '#f9a52c'
        item_overflow_action: Clamp
        item_table:
          0:
          - action: Add
            item: ArmorLight
          - action: Add
            item: GunFSP9
          2:
          - action: Add
            item: Painkillers
          4:
          - action: Add
            item: SCP330
          5:
          - action: Remove
            item: GunFSP9
          - action: Add
            item: GunCrossvec
          6:
          - action: Add
            item: Painkillers
          8:
          - action: Add
            item: SCP330
          - action: Add
            item: GrenadeHE
          10:
          - action: Remove
            item: GunCrossvec
          - action: Add
            item: GunE11SR
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP244a
          11:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          12:
          - action: Remove
            item: ArmorLight
          - action: Add
            item: ArmorCombat
          - action: Add
            item: Painkillers
          - action: Add
            item: GunCom45
          13:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          14:
          - action: Add
            item: Painkillers
          15:
          - action: Remove
            item: GunE11SR
          - action: Add
            item: GunAK
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          - action: Add
            item: SCP268
          16:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP330
          - action: Add
            item: Jailbird
          17:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          18:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP330
          19:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          20:
          - action: Remove
            item: GunAK
          - action: Add
            item: GunLogicer
          - action: Add
            item: SCP018
          - action: Add
            item: GrenadeHE
          21:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          22:
          - action: Add
            item: SCP500
          - action: Add
            item: SCP330
          23:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          24:
          - action: Add
            item: SCP500
          - action: Add
            item: SCP330
          - action: Add
            item: Jailbird
          25:
          - action: Remove
            item: ArmorCombat
          - action: Add
            item: ArmorHeavy
          - action: Add
            item: GunShotgun
          - action: Add
            item: SCP500
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          - action: Add
            item: SCP244b
          26:
          - action: Add
            item: SCP500
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
        ammo_overflow_action: Rollover
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 1
          1:
          - action: Add
            stat: Inventory
            proportion: 0.300000012
          - action: Add
            stat: Gun
            proportion: 0.300000012
        player_overflow_action: Clamp
        player_table:
          5:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          6:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          7:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          8:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          9:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          10:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 5
            sustain: 0
            persistent: false
          11:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 5
            sustain: 1
            persistent: false
          12:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 2
            persistent: false
          13:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 3
            persistent: false
          14:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 15
            sustain: 4
            persistent: false
          15:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 15
            sustain: 5
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          16:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 6
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          17:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 7
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          18:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 25
            sustain: 8
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          19:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 25
            sustain: 9
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          20:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 30
            sustain: 10
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          21:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 30
            sustain: 11
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          22:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 35
            sustain: 12
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          23:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 35
            sustain: 13
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          24:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 40
            sustain: 14
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          25:
          - action: Add
            stat: Stamina
            value: 100
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 40
            sustain: 15
            persistent: false
          - action: Add
            stat: HP
            value: 45
            sustain: 0
            persistent: false
        effect_overflow_action: Clamp
        effect_table:
          5:
          - effect: MovementBoost
            intensity: 10
            duration: 0
          10:
          - effect: MovementBoost
            intensity: 20
            duration: 0
          - effect: BodyshotReduction
            intensity: 1
            duration: 0
          15:
          - effect: MovementBoost
            intensity: 30
            duration: 0
          - effect: BodyshotReduction
            intensity: 2
            duration: 0
          - effect: Scp1853
            intensity: 1
            duration: 0
          20:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          21:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          22:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          23:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          24:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          25:
          - effect: MovementBoost
            intensity: 50
            duration: 0
          - effect: BodyshotReduction
            intensity: 4
            duration: 0
          - effect: Scp1853
            intensity: 3
            duration: 0
      RAGE:
        menu_item: KeycardChaosInsurgency
        menu_description: <color=#008f1c>[混沌钥匙卡]</color>=<b><color=#d64e12>[数据删除]</color></b>
        loadout_lock: true
        color_hex: '#d64e12'
        item_overflow_action: Clamp
        item_table:
          0:
          - action: Add
            item: ArmorLight
          - action: Add
            item: GunCOM15
          1:
          - action: Add
            item: SCP330
          2:
          - action: Add
            item: Painkillers
          3:
          - action: Remove
            item: GunCOM15
          - action: Add
            item: GunCOM18
          4:
          - action: Add
            item: SCP330
          5: []
          6:
          - action: Remove
            item: GunCOM18
          - action: Add
            item: GunRevolver
          - action: Add
            item: Painkillers
          8:
          - action: Add
            item: SCP330
          - action: Add
            item: GrenadeHE
          9:
          - action: Remove
            item: GunRevolver
          - action: Add
            item: GunFSP9
          10:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP244a
          11:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          12:
          - action: Remove
            item: GunFSP9
          - action: Add
            item: GunCrossvec
          - action: Remove
            item: ArmorLight
          - action: Add
            item: ArmorCombat
          - action: Add
            item: Painkillers
          - action: Add
            item: GunCom45
          13:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          14:
          - action: Add
            item: Painkillers
          15:
          - action: Remove
            item: GunCrossvec
          - action: Add
            item: GunE11SR
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          - action: Add
            item: SCP268
          16:
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP330
          - action: Add
            item: Jailbird
          17:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          18:
          - action: Remove
            item: GunE11SR
          - action: Add
            item: GunAK
          - action: Add
            item: Painkillers
          - action: Add
            item: SCP330
          19:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          20:
          - action: Add
            item: SCP018
          - action: Add
            item: GrenadeHE
          21:
          - action: Remove
            item: GunAK
          - action: Add
            item: GunLogicer
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          22:
          - action: Add
            item: SCP500
          - action: Add
            item: SCP330
          23:
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          24:
          - action: Add
            item: GunShotgun
          - action: Add
            item: SCP500
          - action: Add
            item: SCP330
          - action: Add
            item: Jailbird
          25:
          - action: Remove
            item: ArmorCombat
          - action: Add
            item: ArmorHeavy
          - action: Add
            item: SCP500
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
          - action: Add
            item: SCP244b
          26:
          - action: Add
            item: SCP500
          - action: Add
            item: Adrenaline
          - action: Add
            item: SCP330
        ammo_overflow_action: Clamp
        ammo_table:
          0:
          - action: Set
            stat: Inventory
            proportion: 1
          1:
          - action: Add
            stat: Inventory
            proportion: 0.200000003
          2:
          - action: Add
            stat: Inventory
            proportion: 0.200000003
          3:
          - action: Add
            stat: Inventory
            proportion: 0.200000003
          4:
          - action: Add
            stat: Inventory
            proportion: 0.25
          5:
          - action: Add
            stat: Inventory
            proportion: 0.25
          6:
          - action: Add
            stat: Inventory
            proportion: 0.25
          7:
          - action: Add
            stat: Inventory
            proportion: 0.300000012
          8:
          - action: Add
            stat: Inventory
            proportion: 0.300000012
          9:
          - action: Add
            stat: Inventory
            proportion: 0.300000012
          10:
          - action: Add
            stat: Inventory
            proportion: 0.349999994
          - action: Add
            stat: Gun
            proportion: 0.100000001
          11:
          - action: Add
            stat: Inventory
            proportion: 0.349999994
          - action: Add
            stat: Gun
            proportion: 0.100000001
          12:
          - action: Add
            stat: Inventory
            proportion: 0.349999994
          - action: Add
            stat: Gun
            proportion: 0.100000001
          13:
          - action: Add
            stat: Inventory
            proportion: 0.100000001
          - action: Add
            stat: Gun
            proportion: 0.150000006
          14:
          - action: Add
            stat: Inventory
            proportion: 0.100000001
          - action: Add
            stat: Gun
            proportion: 0.150000006
          15:
          - action: Add
            stat: Inventory
            proportion: 0.100000001
          - action: Add
            stat: Gun
            proportion: 0.150000006
          16:
          - action: Add
            stat: Inventory
            proportion: 0.150000006
          - action: Add
            stat: Gun
            proportion: 0.300000012
          17:
          - action: Add
            stat: Inventory
            proportion: 0.150000006
          - action: Add
            stat: Gun
            proportion: 0.300000012
          18:
          - action: Add
            stat: Inventory
            proportion: 0.150000006
          - action: Add
            stat: Gun
            proportion: 0.300000012
          19:
          - action: Add
            stat: Inventory
            proportion: 0.25
          - action: Add
            stat: Gun
            proportion: 0.349999994
          20:
          - action: Add
            stat: Inventory
            proportion: 0.25
          - action: Add
            stat: Gun
            proportion: 0.349999994
          21:
          - action: Add
            stat: Inventory
            proportion: 0.25
          - action: Add
            stat: Gun
            proportion: 0.349999994
          22:
          - action: Add
            stat: Inventory
            proportion: 0.400000006
          - action: Add
            stat: Gun
            proportion: 0.400000006
          23:
          - action: Add
            stat: Inventory
            proportion: 0.400000006
          - action: Add
            stat: Gun
            proportion: 0.400000006
          24:
          - action: Add
            stat: Inventory
            proportion: 0.400000006
          - action: Add
            stat: Gun
            proportion: 0.400000006
          25:
          - action: Add
            stat: Inventory
            proportion: 0.5
          - action: Add
            stat: Gun
            proportion: 0.5
        player_overflow_action: Clamp
        player_table:
          5:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          6:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          7:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          8:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          9:
          - action: Add
            stat: Stamina
            value: 20
            sustain: 0
            persistent: false
          10:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 5
            sustain: 0
            persistent: false
          11:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 5
            sustain: 2
            persistent: false
          12:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 4
            persistent: false
          13:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 10
            sustain: 6
            persistent: false
          14:
          - action: Add
            stat: Stamina
            value: 40
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 15
            sustain: 8
            persistent: false
          15:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 15
            sustain: 10
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          16:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 12
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          17:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 20
            sustain: 14
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          18:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 25
            sustain: 16
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          19:
          - action: Add
            stat: Stamina
            value: 60
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 25
            sustain: 18
            persistent: false
          - action: Add
            stat: HP
            value: 15
            sustain: 0
            persistent: false
          20:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 30
            sustain: 20
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          21:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 30
            sustain: 22
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          22:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 35
            sustain: 24
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          23:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 35
            sustain: 26
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          24:
          - action: Add
            stat: Stamina
            value: 80
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 40
            sustain: 28
            persistent: false
          - action: Add
            stat: HP
            value: 30
            sustain: 0
            persistent: false
          25:
          - action: Add
            stat: Stamina
            value: 100
            sustain: 0
            persistent: false
          - action: Add
            stat: AHP
            value: 40
            sustain: 30
            persistent: false
          - action: Add
            stat: HP
            value: 45
            sustain: 0
            persistent: false
        effect_overflow_action: Clamp
        effect_table:
          0:
          - effect: Hemorrhage
            intensity: 1
            duration: 0
          - effect: Burned
            intensity: 1
            duration: 0
          - effect: Exhasted
            intensity: 1
            duration: 0
          - effect: Disabled
            intensity: 1
            duration: 0
          - effect: Bleeding
            intensity: 1
            duration: 0
          3:
          - effect: Bleeding
            intensity: 0
            duration: 0
          5:
          - effect: MovementBoost
            intensity: 10
            duration: 0
          6:
          - effect: Disabled
            intensity: 0
            duration: 0
          9:
          - effect: Exhausted
            intensity: 0
            duration: 0
          10:
          - effect: MovementBoost
            intensity: 20
            duration: 0
          - effect: BodyshotReduction
            intensity: 1
            duration: 0
          12:
          - effect: Burned
            intensity: 0
            duration: 0
          15:
          - effect: Heomorrhage
            intensity: 0
            duration: 0
          - effect: MovementBoost
            intensity: 30
            duration: 0
          - effect: BodyshotReduction
            intensity: 2
            duration: 0
          - effect: Scp1853
            intensity: 1
            duration: 0
          20:
          - effect: MovementBoost
            intensity: 40
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          21:
          - effect: MovementBoost
            intensity: 46
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          22:
          - effect: MovementBoost
            intensity: 52
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          23:
          - effect: MovementBoost
            intensity: 58
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          24:
          - effect: MovementBoost
            intensity: 64
            duration: 0
          - effect: BodyshotReduction
            intensity: 3
            duration: 0
          - effect: Scp1853
            intensity: 2
            duration: 0
          25:
          - effect: MovementBoost
            intensity: 72
            duration: 0
          - effect: BodyshotReduction
            intensity: 4
            duration: 0
          - effect: Scp1853
            intensity: 3
            duration: 0
  loadout_config:
    is_black_list_enabled: true
    # put black listed weapons here, see below for all weapons. does not effect weapons granted as a reward only guns on the menu
    black_list: []
    # list of all the different weapons (changing this does nothing)
    all_weapons:
    - GunAK
    - GunCOM15
    - GunCOM18
    - GunCom45
    - GunCrossvec
    - GunE11SR
    - GunFSP9
    - GunLogicer
    - GunRevolver
    - GunShotgun
  lobby_config:
    spawn_color_red: 67
    spawn_color_green: 191
    spawn_color_blue: 240
    spawn_light_intensity: 100
    # max players should be less than SpawnDimX x SpawnDimY
    spawn_dim_x: 8
    spawn_dim_y: 8
    spawn_protection: 3
  experience_config:
    is_enabled: true
    # log how much xp and the reason to the recievers gameconsole
    game_console_log: true
    # xp rewards. xp is only saved at the end of the round, if a player leaves before the round ends they will not receive any xp
    xp_per_kill: 50
    xp_per_minute: 20
    xp_on100_damage: 25
    xp_on500_damage: 250
    xp_on2500_damage: 2500
    xp_on10000_damage: 20000
    xp_on5_killstreak: 250
    xp_on10_killstreak: 1000
    xp_on15_killstreak: 2000
    xp_on20_killstreak: 4000
    xp_on25_killstreak: 10000
    # xp granted 30 seconds into the round to reward players that stay an entire round
    xp_on_round_start: 1200
    # see the global reference config for all item types
    xp_on_item_use:
      Painkillers: 5
      Medkit: 5
      SCP330: 10
      Adrenaline: 15
      GrenadeFlash: 30
      SCP244a: 50
      SCP244b: 50
      GrenadeHE: 60
      SCP207: 100
      SCP1853: 100
      SCP500: 150
      SCP2176: 150
      SCP268: 300
    # see the global reference config for all item types
    xp_on_item_thrown:
      SCP244a: 50
      SCP244b: 50
      SCP2176: 150
      SCP018: 300
    # xp leveling - to fine tune these values use this calculator https://www.desmos.com/calculator/1dqftattpd
    base_xp_level: 250
    level_exponent: 1.10000002
    stage_exponent: 1.25
    tier_exponent: 1.5
    xp_rounding: 5
    # value = xp to next level
    badge_format: '{tier} | {stage} | {level} | {value}'
    leader_board_format: '{tier} {stage} {level}'
    xp_to_next_level_format: 'XP: {xp}/{max}'
    level_tags:
    - 'Level: 1'
    - 'Level: 2'
    - 'Level: 3'
    - 'Level: 4'
    - 'Level: 5'
    - 'Level: 6'
    - 'Level: 7'
    - 'Level: 8'
    - 'Level: 9'
    - 'Level: 10'
    - 'Level: 11'
    - 'Level: 12'
    - 'Level: 13'
    - 'Level: 14'
    - 'Level: 15'
    - 'Level: 16'
    - 'Level: 17'
    - 'Level: 18'
    - 'Level: 19'
    - 'Level: 20'
    - 'Level: 21'
    - 'Level: 22'
    - 'Level: 23'
    - 'Level: 24'
    - 'Level: 25'
    stage_tags:
    - 'Stage: 1'
    - 'Stage: 2'
    - 'Stage: 3'
    - 'Stage: 4'
    - 'Stage: 5'
    - 'Stage: 6'
    - 'Stage: 7'
    tier_tags:
    - 'Tier: 1'
    - 'Tier: 2'
    - 'Tier: 3'
    - 'Tier: 4'
    - 'Tier: 5'
    - 'Tier: 6'
    - 'Tier: 7'
    leader_board_level_tags:
    - L:1
    - L:2
    - L:3
    - L:4
    - L:5
    - L:6
    - L:7
    - L:8
    - L:9
    - L:10
    - L:11
    - L:12
    - L:13
    - L:14
    - L:15
    - L:16
    - L:17
    - L:18
    - L:19
    - L:20
    - L:21
    - L:22
    - L:23
    - L:24
    - L:25
    leader_board_stage_tags:
    - S:1
    - S:2
    - S:3
    - S:4
    - S:5
    - S:6
    - S:7
    leader_board_tier_tags:
    - T:1
    - T:2
    - T:3
    - T:4
    - T:5
    - T:6
    - T:7
    xp_cmd_permissions:
    - ServerConsoleCommands
  rank_config:
    is_enabled: true
    badge_format: >
      Rank: {name}
    leader_board_format: '{tag}'
    dnt_name: Do Not Track
    dnt_tag: DNT
    dnt_color: nickel
    # players start unranked. unranked players cannot influence placement/ranked players. once the MinXpForPlacement is achieved they will progress to placement
    unranked_name: Unranked
    unranked_tag: '----'
    unranked_color: nickel
    # minimum rank before players obtain placement rank status. values start at 0 not 1. e.g. level 0 = L:1 level 1 = L2
    min_xp_for_placement:
      value: 0
      level: 0
      stage: 1
      tier: 0
    # placement players rank is influenced by other placement players and ranked players but ranked players are not influenced by placement players
    placement_name: Placement
    placement_tag: '?'
    placement_color: magenta
    # matches referes to kill/deaths against placement and ranked players. this is how many until you become ranked
    placement_matches: 150
    # ranks must be in order of least rating to most rating and colors must be a valid servergroup colors see https://en.scpslgame.com/index.php/Docs:Permissions 
#All players start out with a 1500 rating when they enter placement. Ranks should be scaled around the 1500 mark.
    ranks:
    - name: Silver I
      tag: S1
      rating: -150
      color: nickel
    - name: Silver II
      tag: S2
      rating: 0
      color: nickel
    - name: Silver III
      tag: S3
      rating: 150
      color: nickel
    - name: Silver IV
      tag: S4
      rating: 300
      color: nickel
    - name: Silver Elite
      tag: SE
      rating: 550
      color: silver
    - name: Silver Elite Master
      tag: SEM
      rating: 700
      color: silver
    - name: Gold Nova I
      tag: GN1
      rating: 950
      color: cyan
    - name: Gold Nova II
      tag: GN2
      rating: 1100
      color: cyan
    - name: Gold Nova III
      tag: GN3
      rating: 1350
      color: cyan
    - name: Gold Nova Master
      tag: GNM
      rating: 1500
      color: aqua
    - name: Master Guardian I
      tag: MG1
      rating: 1650
      color: blue_green
    - name: Master Gaurdian II
      tag: MG2
      rating: 1800
      color: blue_green
    - name: Master Gaurdian Elite
      tag: MGE
      rating: 1950
      color: emerald
    - name: Distinguished Master Gaurdian
      tag: DMG
      rating: 2100
      color: mint
    - name: Legendary Eagle
      tag: LE
      rating: 2250
      color: yellow
    - name: Legendary Eagle Master
      tag: LEM
      rating: 2400
      color: yellow
    - name: Supreme Master First Class
      tag: SMFC
      rating: 2550
      color: orange
    - name: Global Elite
      tag: GE
      rating: 2700
      color: crimson
    rank_cmd_permissions:
    - ServerConsoleCommands
  tracking_config:
    is_enabled: true
    track_hits: true
    track_loadouts: true
    track_rounds: true
    tracking_cmd_permissions:
    - ServerConsoleCommands
  translation_config:
  # 主要信息
    dnt_msg: <size=64><b><color=#FF0000>警告！您已启用DNT，因此您获得的任何经验和等级都将在回合结束时丢失！由于您在游戏设置中启用了DNT，您的配置、偏好设置、连杀、统计数据、角色和装备无法保存。为了保持公平，您对排名玩家的击杀/死亡将被忽略。</color></b></size>
    # 命中部位
    body: 身体
    limb: 四肢
    head: 头部
    # 击杀信息
    firearm_kill: '{killer} 用 {gun} 击中了 {victim} 的 {hitbox}'
    explosion_kill: '{killer} 炸死了 {victim}'
    explosion_self_kill: '{victim} 被自己的 <b><color=#eb0d47>破片手榴弹</color></b> 炸死了'
    jailbird_head_kill: '{killer} 用 <b><color=#eb0d47>囚鸟</color></b> 敲击了 {victim} 的 {hitbox}'
    jailbird_normal_kill: '{killer} 用 <b><color=#eb0d47>囚鸟</color></b> 拍击了 {victim} 的 {hitbox}'
    scp018_kill: '{killer} 用 <b><color=#eb0d47>SCP 018</color></b> 击杀了 {victim}'
    scp018_self_kill: '{victim} 因为没能接住自己的球而自取其辱'
    distruptor_kill: '{killer} 用 <b><color=#eb0d47>分子裂解者</color></b> 分解了 {victim} 的 {hitbox}'
    distruptor_self_kill: '{victim} 被自己的 <b><color=#eb0d47>分子裂解者</color></b> 分解了'
    custom_reason_kill: '{victim} 被击杀：<b><color=#43BFF0>{reason}</color></b>'
    failed_first_grade: '{victim} <b><color=#eb0d47>不识字所以离开了比赛，自取其辱</color></b>'
    self_kill: <b><color=#eb0d47>{victim}</color></b> 自取其辱
    # 连杀
    global_killstreak: <b>{killstreak} <color=#43BFF0>{name}</color></b> 正在进行 <b><color=#FF0000>{count}</color></b> 连杀
    private_killstreak: 连杀 <b><color=#FF0000>{count}</color></b>
    global_killstreak_ended: <b>{killer_killstreak} <color=#43BFF0>{killer}</color></b> 终结了 <b>{victim_killstreak} <color=#43BFF0>{victim}</color></b> 的 <b><color=#FF0000>{count}</color></b> 连杀
    # 装备
    customisation_hint: <b>检查背包！<color=#FF0000>右键点击O5钥匙卡选择武器</color></b>
    customisation_denied:
    - <color=#f8d107>开火/使用物品后无法自定义装备</color>
    - <color=#43BFF0>等待下次重生</color>
    last_weapon: <color=#f8d107>最后一把武器无法删除！</color>
    weapon_banned: <color=#FF0000>{weapon} 当前被禁用</color>
    # 大厅
    teleport: <color=#43BFF0>选择武器后您将被传送</color>
    waiting_for_players:
    - <color=#43BFF0>等待1名玩家加入</color>
    - <color=#43BFF0>您可以选择起始区域！(目前存在BUG，等待修复)</color>
    respawn: <b><color=#FFFF00>左键/右键点击重生</color></b>
    attachments: <b><color=#FF0000>按Tab编辑配件/预设</color></b>
    teleporting: <color=#43BFF0>7秒后传送</color>
    teleport_cancel: <color=#43BFF0>打开[主菜单]取消</color> - <color=#FF0000>右键点击O5钥匙卡</color>
    fast_teleport: <color=#43BFF0>装备已设置，3秒后传送</color>
    spectator_mode: 观察scp-939重生，如果出现错误，您可能需要离开并重新加入以重生
    # main menu
    main_menu: <b><color=#43BFF0>[主菜单]</color></b> <b><color=#FF0000>右键点击选择</color></b>
    back_to_main_menu: <color=#5d318c>[O5钥匙卡]</color> = <b>返回 <color=#43BFF0>[主菜单]</color></b>
    save_and_exit: <color=#5d318c>[O5钥匙卡]</color> = <b><color=#5d318c>保存并退出</color></b>
    customise_loadout: <color=#e7d77b>[科学家钥匙卡]</color> = <b><color=#FF0000>自定义装备 - </color><color=#43BFF0>[武器槽位]</color></b>
    killstreak_reward_system: <color=#e1ab21>[研究主管钥匙卡]</color> = <b><color=#43BFF0>[连杀奖励系统]</color></b>
    role: <color=#bd8f86>[收容工程师钥匙卡]</color> = <b><color=#43BFF0>[角色]</color></b>
    preferences: <color=#bd1a4a>[设施总监钥匙卡]</color> = <b><color=#43BFF0>[偏好设置]</color></b>
    # gun slot menu
    gun_slot_menu: <b><color=#43BFF0>[武器槽位]</color></b> <b><color=#FF0000>右键点击选择</color></b>
    primary: <color=#bd1a4a>[设施总监钥匙卡]</color> = <b><color=#FF0000>主武器 - </color><color=#43BFF0>[武器类别]</color></b>
    secondary: <color=#217b7b>[区域总监钥匙卡]</color> = <b>副武器 - <color=#43BFF0>[武器类别]</color></b>
    heavy_primary: <color=#1b43cb>[指挥官钥匙卡]</color> = <b><color=#FF0000>主武器 - </color><color=#43BFF0>[武器类别]</color></b>
    heavy_secondary: <color=#177dde>[中士钥匙卡]</color> = <b>副武器 - <color=#43BFF0>[武器类别]</color></b>
    heavy_tertiary: <color=#accfe1>[列兵钥匙卡]</color> = <b>第三武器 - <color=#43BFF0>[武器类别]</color></b>
    # gun class menu
    gun_class_menu: <b><color=#43BFF0>[武器类别]</color></b> <b><color=#FF0000>右键点击选择</color></b>
    mtf_guns: <color=#1b43cb>[指挥官钥匙卡]</color> = <b><color=#1b43cb>[MTF武器]</color></b>
    chaos_guns: <color=#008f1c>[混沌钥匙卡]</color> = <b><color=#008f1c>[混沌武器]</color></b>
    # gun menu
    mtf_gun_menu: <b><color=#1b43cb>[MTF武器]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    chaos_gun_menu: <b><color=#008f1c>[混沌武器]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    gun_selected: <color=#43BFF0>{gun}</color> 已添加到您的装备中作为 <color=#FF0000>{slot}</color> 武器
    # killstreak reward system menu
    killstreak_reward_menu: <b><color=#43BFF0>[连杀奖励系统]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    killstreak_selected: '{killstreak} 已选择为您的连杀奖励系统'
    current_killstreak_selected: '当前选择的连杀奖励系统：{killstreak}</color>'
    # role menu
    role_menu: <b><color=#43BFF0>[角色]</color></b> <b><color=#FF0000>右键点击选择</color></b>
    class_d: <color=#bdafe4>[清洁工钥匙卡]</color> = <b><color=#FF8E00>D级人员</color></b>
    scientist: <color=#e7d77b>[科学家钥匙卡]</color> = <b><color=#FFFF7C>科学家</color></b>
    guard: <color=#5B6370>[警卫钥匙卡]</color> = <b><color=#5B6370>设施警卫</color></b>
    private: <color=#accfe1>[列兵钥匙卡]</color> = <b><color=#accfe1>NTF列兵</color></b>
    sergeant: <color=#177dde>[中士钥匙卡]</color> = <b><color=#177dde>NTF中士</color></b>
    captain: <color=#1b43cb>[指挥官钥匙卡]</color> = <b><color=#1b43cb>NTF指挥官</color></b>
    chaos: <color=#008f1c>[混沌钥匙卡]</color> = <b><color=#008f1c>混沌分裂者</color></b>
    role_selected: '{role} 已选择为角色'
    # preferences menu
    preferences_menu: <b><color=#43BFF0>[偏好设置]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    stats: <color=#eb0d47>[科学家钥匙卡]</color> = <b><color=#43BFF0>[统计数据]</color></b>
    spectator: <color=#eb0d47>[手电筒]</color> = <b>启用观察者模式</b>
    enable_rage: <color=#eb0d47>[硬币]</color> = <b>启用 [数据删除]</b>
    delete_data: <color=#eb0d47>[清洁工钥匙卡]</color> = <b>删除数据 统计/配置/排名/经验/偏好设置 (无法撤销)</b>
    leader_board: <color=#eb0d47>[SCP1576]</color> = <b><color=#43BFF0>[排行榜]</color></b>
    # stats menu
    stats_menu: <b><color=#43BFF0>[统计数据]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    back_to_preferences: <color=#5d318c>[O5钥匙卡]</color> = <b>返回 <color=#43BFF0>[偏好设置]</color></b>
    # confirm delete data menu
    delete_data_menu: <b><color=#43BFF0>[数据删除确认]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    are_you_sure: <b>您确定吗？您必须在设置中启用"不跟踪"才能正常工作。<color=#FF0000>删除操作无法撤销。</color> <color=#43BFF0>如果确定，请右键点击清洁工钥匙卡删除您的数据</color></b>
    failed_to_delete_data: <b>删除数据失败，您必须在设置中启用"不跟踪"</b>
    deleted_data: 数据已删除
    # leader board menu
    leader_board_menu: <b><color=#43BFF0>[排行榜]</color></b> - <b><color=#FF0000>右键点击选择</color></b>
    # rooms
    second_player_joined: 玩家 <color=#43BFF0>{name}</color> 加入了，等待他们选择装备
    second_player_help: <color=#43BFF0>{name}</color> 在设置装备时遇到困难，请用您的语音帮助他
    decontaminating: <color=#FF0000>正在净化！</color>
    caution: <color=#FFFF00>注意！房间将在 {time} 后净化</color>
    warning: <color=#FF8000>警告！房间将在 {time} 后净化</color>
    danger: <color=#FF0000>危险！净化即将开始！{time}</color>
    # stats
    death_msg_killer: >2-



      {killer} <color=#43BFF0>HP: {health}</color>
    death_msg_ahp: ' <color=#008f1c>AH: {ahp}</color>'
    death_msg_damage_reduction: ' <color=#5900ff> DR: {reduction}%</color>'
    death_msg_bodyshot_reduction: ' <color=#e7d77b> BSR: {reduction}%</color>'
    death_msg_damage_delt: >2-

      <color=#43BFF0>Delt - DMG: {damage}</color> <color=#FF0000>HS: {head_shots}</color> <color=#36a832>BS: {body_shots}</color> <color=#43BFF0>LS: {limb_shots}</color>
    death_msg_damage_taken: >2-

      <color=#43BFF0>Taken - DMG: {damage}</color> <color=#FF0000>HS: {head_shots}</color> <color=#36a832>BS: {body_shots}</color> <color=#43BFF0>LS: {limb_shots}</color>
    death_msg_damage_other: ' Other: {other_hits}'
    player_stats_line1: <color=#76b8b5>Kills:</color> <color=#FF0000>{kills}</color>    <color=#76b8b5>Deaths:</color> <color=#FF0000>{deaths}</color>    <color=#76b8b5>K/D:</color> <color=#FF0000>{kd}</color>    <color=#76b8b5>Highest Killstreak:</color> <color=#FF0000>{top_ks}</color></color>    <color=#76b8b5>Score:</color> <color=#FF0000>{score}</color>
    player_stats_line2: <color=#76b8b5>Hs Kills:</color> <color=#FF0000>{hsk}%</color>    <color=#76b8b5>Hs:</color> <color=#FF0000>{hs}%</color>    <color=#76b8b5>Accuracy:</color> <color=#FF0000>{accuracy}%</color>    <color=#76b8b5>Dmg Delt:</color> <color=#FF0000>{dmg_delt}</color>    <color=#76b8b5>Dmg Taken:</color> <color=#FF0000>{dmg_taken}</color>
    highest_killstreak: <b><color=#43BFF0>{name}</color></b> <color=#d4af37>had the highest killstreak of</color> <b><color=#FF0000>{streak}</color></b>
    highest_kills: <b><color=#43BFF0>{name}</color></b> <color=#c0c0c0>had the most kills</color> <b><color=#FF0000>{kills}</color></b>
    highest_score: <b><color=#43BFF0>{name}</color></b> <color=#a97142>was the best player with a score of </color> <b><color=#FF0000>{score}</color></b>
    # experience
    reward_xp_kill: gained {xp} Xp for killing player
    reward_xp_minute: gained {xp} Xp for playing for {time} minutes
    reward_xp100_damage: gained {xp} Xp for dealing 100 damage
    reward_xp500_damage: gained {xp} Xp for dealing 500 damage
    reward_xp2500_damage: gained {xp} Xp for dealing 2500 damage
    reward_xp10000_damage: gained {xp} Xp for dealing 10000 damage
    reward_xp5_killstreak: gained {xp} Xp for reaching a 5 killstreak
    reward_xp10_killstreak: gained {xp} Xp for reaching a 10 killstreak
    reward_xp15_killstreak: gained {xp} Xp for reaching a 15 killstreak
    reward_xp20_killstreak: gained {xp} Xp for reaching a 20 killstreak
    reward_xp25_killstreak: gained {xp} Xp for reaching a 25 killstreak
    reward_xp_round_start: gained {xp} Xp from round start bonus
    reward_xp_item_used: gained {xp} Xp for using {item}
    reward_xp_item_thrown: gained {xp} Xp for throwing {item}
    xp_msg: >
      <align=center><voffset=2em><b><size=48>{xp}</size></b>
    xp_gained_msg: >
      <align=center><voffset=2em><b><size=48>YOU GAINED {xp} XP THIS ROUND!</size></b>
    # rank
    rank_msg: >
      <align=center><voffset=1.5em><b><size=72><color={color}>{rank}</color></size></b>
    # attachment blacklist
    attachment_banned: <color=#FF0000>attachment {attachment} banned</color>
    # voice chat
    global_talk_global_receive: '<color=#43BFF0>Voice Chat: Global Talk Global Receive</color>'
    proximity_talk_global_receive: <color=#43BFF0>Voice Chat:</color> <color=#FF0000>Proximity Talk</color> <color=#43BFF0>Global Receive</color>
    proximity_talk_proximity_receive: <color=#43BFF0>Voice Chat:</color> <color=#FF0000>Proximity Talk Proximity Receive</color>
    # leader board
    leader_board_title: <color=#d4af37><b><size=128>Leader Board</size></b></color>
    leader_board_control: >2-

      <size=24><b><color=#FF0000>Controls: uses player movement for page/menu scrolling Left/Right = Type, Forward/Backward = Page. Note, wont work when up against a wall</color></b></size>
    ledgend_pos: Pos
    ledgend_name: Name
    ledgend_rank: Rank
    ledgend_experience: Experience
    ledgend_killstreak: Killstreak
    ledgend_kills: Kills
    ledgend_time: Time
    page_and_line: Page {page} of {page_max} [{line_start} - {line_end}]/{line_max}
    # round
    round_end5_minutes: <color=#43BFF0>Round Ends in 5 minutes</color>
    round_end1_minute: <color=#43BFF0>Round Ends in 1 minute</color>
  attachment_blacklist_config:
    is_enabled: true
    # put black listed attachments here, see global reference config for attachment types
    black_list: 
    - ShotgunDoubleShot
    - ScopeSight
    
  voice_chat_config:
    is_enabled: true
  cleanup_config:
    is_enabled: true
    # items to not cleanup when the round start e.g. scp207, medkits ect... [see global reference config for types]
    initial_cleanup_whitelist: []
    # items to cleanup throughout the round if dropped by player [see global reference config for types]
# armor, gun, keycards are automaticaly deleted
    item_cleanup_blacklist:
    - Jailbird
    # how often to cleanup ragdolls in seconds. -1 = never
    ragdoll_cleanup_period: -1
  leader_board_config:
    is_enabled: true
    # Sessions before this date are ignored in the leader board, applies to total kills, highest killstreak and total time. leaderboard needs to be rebuilt with dmrblb in RA console to apply the changes
    begin_epoch: 2025-01-01T00:00:00.0000000
    # Sessions after this date are ignored in the leader board, applies to total kills, highest killstreak and total time. leaderboard needs to be rebuilt with dmrblb in RA console to apply the changes
    end_epoch: 2025-04-01T00:00:00.0000000
    # How often to advance the Epoch in months. Triggered when the current date is beyond the EndEpoch
    auto_increment_period: 3
    lines_per_page: 26
    # the delay in seconds after the round ends before showing all the players the leaderboard
    display_end_round_delay: 5
    # which leader board to display at round end -1 = random, 0 = Rank, 1 = Experience, 2 = Killstreak, 3 = Kills, 4 = Time
    leader_board_type: -1
    # table
    v_offset: <voffset=7em>
    format: <size=29><line-height=75%><mspace=0.55em>
    ledgend_color: <color=#43BFF0>
    ledgend_highlight_color: <color=#FF0000>
    record_color: <color=#43BFF0>
    record_highlight_color: <color=#FF0000>
    table_color: <color=#5d318c>
    position_width: 3
    name_width: 18
    killstreak_value_width: 3
    kills_width: 5
    time_width: 4
    # table characters
    top_left_corner: ╻
    top_junction: ┯
    top_right_corner: ╻
    left_junction: ┃
    vertical: ┃
    horizontal: ━
    ledgend_vertical: │
    right_junction: ┃
    ledgend_horizontal: ─
    ledgend_intersection: ┼
    record_separator: │
    bottom_left_corner: ╹
    bottom_junction: ┷
    bottom_right_corner: ╹
    # use this after changing the start/end epoch. rebuilding might be very slow
    lb_cmd_permissions:
    - ServerConsoleCommands
